#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
防守一团表现分析程序
分析防守一团各职业玩家在对局中的表现，并生成关键结果汇总
"""

import json
import statistics
from typing import Dict, List, Tuple, Any

def load_data(file_path: str) -> List[Dict]:
    """加载对局数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def load_defense_team_list(file_path: str) -> List[str]:
    """加载防守一团名单"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
        return [name.strip() for name in content.split(',')]

def calculate_player_performance(player: Dict) -> Dict:
    """计算玩家个人表现指标"""
    # 基础数据
    kills = player['击败数']
    deaths = max(player['死亡数'], 1)  # 避免除零
    assists = player['助攻数']
    player_damage = player['对玩家伤害']
    building_damage = player['对建筑伤害']
    healing = player['治疗值']
    revives = player['化羽/清泉数']
    burns = player['焚骨数']
    damage_taken = player['承受伤害']
    
    # 计算关键指标
    kd_ratio = kills / deaths
    total_damage = player_damage + building_damage
    damage_per_death = total_damage / deaths
    player_damage_per_death = player_damage / deaths
    
    # 人伤专注度
    if total_damage > 0:
        player_damage_focus = player_damage / total_damage
    else:
        player_damage_focus = 0
    
    # 治疗效率（仅素问）
    healing_per_death = healing / deaths if healing > 0 else 0
    
    # 生存能力
    damage_taken_per_death = damage_taken / deaths
    
    return {
        'kd_ratio': kd_ratio,
        'total_damage': total_damage,
        'damage_per_death': damage_per_death,
        'player_damage_per_death': player_damage_per_death,
        'player_damage_focus': player_damage_focus,
        'healing_per_death': healing_per_death,
        'damage_taken_per_death': damage_taken_per_death,
        'assists_per_death': assists / deaths,
        'revives': revives,
        'burns': burns
    }

def analyze_defense_team_performance(data: List[Dict], defense_team: List[str]) -> Dict:
    """分析防守一团整体表现"""
    # 筛选防守一团成员
    defense_players = [p for p in data if p['玩家名字'] in defense_team and p['队伍'] == '星河若梦']
    
    if not defense_players:
        return {"error": "未找到防守一团成员数据"}
    
    # 计算团队基础数据
    team_stats = {
        'team_size': len(defense_players),
        'total_kills': sum(p['击败数'] for p in defense_players),
        'total_deaths': sum(p['死亡数'] for p in defense_players),
        'total_assists': sum(p['助攻数'] for p in defense_players),
        'total_player_damage': sum(p['对玩家伤害'] for p in defense_players),
        'total_building_damage': sum(p['对建筑伤害'] for p in defense_players),
        'total_healing': sum(p['治疗值'] for p in defense_players),
        'total_damage_taken': sum(p['承受伤害'] for p in defense_players)
    }
    
    # 计算团队平均值
    team_stats['avg_kills'] = team_stats['total_kills'] / team_stats['team_size']
    team_stats['avg_deaths'] = team_stats['total_deaths'] / team_stats['team_size']
    team_stats['team_kd_ratio'] = team_stats['total_kills'] / max(team_stats['total_deaths'], 1)
    
    # 人伤专注度
    total_damage = team_stats['total_player_damage'] + team_stats['total_building_damage']
    team_stats['player_damage_focus'] = team_stats['total_player_damage'] / max(total_damage, 1)
    
    # 分析各职业表现
    profession_analysis = analyze_by_profession(defense_players)
    
    # 个人表现分析
    player_performances = []
    for player in defense_players:
        perf = calculate_player_performance(player)
        perf['name'] = player['玩家名字']
        perf['profession'] = player['职业']
        perf['raw_data'] = player
        player_performances.append(perf)
    
    # 排序和评级
    player_performances.sort(key=lambda x: x['kd_ratio'], reverse=True)
    
    return {
        'team_stats': team_stats,
        'profession_analysis': profession_analysis,
        'player_performances': player_performances,
        'defense_players': defense_players
    }

def analyze_by_profession(defense_players: List[Dict]) -> Dict:
    """按职业分析表现"""
    profession_stats = {}
    
    for player in defense_players:
        profession = player['职业']
        if profession not in profession_stats:
            profession_stats[profession] = {
                'count': 0,
                'players': [],
                'total_kills': 0,
                'total_deaths': 0,
                'total_player_damage': 0,
                'total_healing': 0,
                'total_revives': 0,
                'total_burns': 0
            }
        
        stats = profession_stats[profession]
        stats['count'] += 1
        stats['players'].append(player['玩家名字'])
        stats['total_kills'] += player['击败数']
        stats['total_deaths'] += player['死亡数']
        stats['total_player_damage'] += player['对玩家伤害']
        stats['total_healing'] += player['治疗值']
        stats['total_revives'] += player['化羽/清泉数']
        stats['total_burns'] += player['焚骨数']
    
    # 计算各职业平均值
    for profession, stats in profession_stats.items():
        if stats['count'] > 0:
            stats['avg_kills'] = stats['total_kills'] / stats['count']
            stats['avg_deaths'] = stats['total_deaths'] / stats['count']
            stats['avg_player_damage'] = stats['total_player_damage'] / stats['count']
            stats['kd_ratio'] = stats['total_kills'] / max(stats['total_deaths'], 1)
    
    return profession_stats

def identify_key_issues(analysis: Dict) -> List[str]:
    """识别关键问题"""
    issues = []
    team_stats = analysis['team_stats']
    player_performances = analysis['player_performances']
    
    # 团队KD比问题
    if team_stats['team_kd_ratio'] < 0.8:
        issues.append(f"团队KD比过低: {team_stats['team_kd_ratio']:.2f}")
    
    # 人伤专注度问题
    if team_stats['player_damage_focus'] < 0.7:
        issues.append(f"人伤专注度不足: {team_stats['player_damage_focus']:.1%}")
    
    # 高死亡率玩家
    high_death_players = [p for p in player_performances if p['raw_data']['死亡数'] >= 10]
    if high_death_players:
        issues.append(f"高死亡率玩家: {len(high_death_players)}人死亡≥10次")
    
    # 零击败玩家（非素问）
    zero_kill_non_healer = [p for p in player_performances 
                           if p['raw_data']['击败数'] == 0 and p['profession'] != '素问']
    if zero_kill_non_healer:
        issues.append(f"零击败非治疗玩家: {len(zero_kill_non_healer)}人")
    
    # 素问表现问题
    suwen_players = [p for p in player_performances if p['profession'] == '素问']
    if suwen_players:
        avg_healing = statistics.mean([p['raw_data']['治疗值'] for p in suwen_players])
        if avg_healing < 100000000:  # 1亿治疗量
            issues.append(f"素问平均治疗量偏低: {avg_healing:,.0f}")
    
    return issues

def generate_report(analysis: Dict, issues: List[str]) -> str:
    """生成分析报告"""
    team_stats = analysis['team_stats']
    profession_analysis = analysis['profession_analysis']
    player_performances = analysis['player_performances']
    
    report = []
    report.append("## 10. 防守一团专项分析")
    report.append("")
    report.append("### 10.1 防守一团整体表现")
    report.append("")
    report.append(f"**团队构成**: {team_stats['team_size']}人")
    report.append("**整体评估**:")
    report.append(f"- 团队KD比: {team_stats['team_kd_ratio']:.2f}")
    report.append(f"- 平均死亡数: {team_stats['avg_deaths']:.1f}次/人")
    report.append(f"- 人伤专注度: {team_stats['player_damage_focus']:.1%}")
    report.append(f"- 总击败数: {team_stats['total_kills']}次")
    report.append(f"- 总死亡数: {team_stats['total_deaths']}次")
    report.append("")
    
    report.append("### 10.2 职业表现分析")
    report.append("")
    report.append("| 职业 | 人数 | 平均击败数 | 平均死亡数 | KD比 | 平均人伤 | 表现评价 |")
    report.append("|------|------|-----------|-----------|------|----------|----------|")
    
    for profession, stats in profession_analysis.items():
        avg_damage_str = f"{stats['avg_player_damage']:,.0f}"
        performance_rating = "优秀" if stats['kd_ratio'] > 2 else "良好" if stats['kd_ratio'] > 1 else "需改进"
        report.append(f"| {profession} | {stats['count']} | {stats['avg_kills']:.1f} | {stats['avg_deaths']:.1f} | {stats['kd_ratio']:.2f} | {avg_damage_str} | {performance_rating} |")
    
    report.append("")
    
    # 关键问题
    report.append("### 10.3 关键问题识别")
    report.append("")
    for i, issue in enumerate(issues, 1):
        report.append(f"{i}. **{issue}**")
    report.append("")
    
    # 个人表现排行
    report.append("### 10.4 个人表现排行 (按KD比)")
    report.append("")
    report.append("| 排名 | 玩家 | 职业 | 击败数 | 死亡数 | KD比 | 对玩家伤害 | 助攻数 |")
    report.append("|------|------|------|--------|--------|------|----------|--------|")
    
    for i, player in enumerate(player_performances[:10], 1):  # 显示前10名
        raw = player['raw_data']
        report.append(f"| {i} | {player['name']} | {player['profession']} | {raw['击败数']} | {raw['死亡数']} | {player['kd_ratio']:.2f} | {raw['对玩家伤害']:,} | {raw['助攻数']} |")
    
    report.append("")
    
    # 素问专项分析
    suwen_players = [p for p in player_performances if p['profession'] == '素问']
    if suwen_players:
        report.append("### 10.5 素问玩家专项分析")
        report.append("")
        report.append("| 玩家 | 治疗值 | 化羽数 | 死亡数 | 治疗效率 | 表现评价 |")
        report.append("|------|--------|--------|--------|----------|----------|")
        
        for player in suwen_players:
            raw = player['raw_data']
            healing_eff = player['healing_per_death']
            rating = "优秀" if healing_eff > 20000000 else "良好" if healing_eff > 15000000 else "需改进"
            report.append(f"| {player['name']} | {raw['治疗值']:,} | {raw['化羽/清泉数']} | {raw['死亡数']} | {healing_eff:,.0f} | {rating} |")
        
        report.append("")
    
    # 问题玩家详情
    problem_players = [p for p in player_performances 
                      if p['raw_data']['死亡数'] >= 10 or 
                         (p['raw_data']['击败数'] == 0 and p['profession'] != '素问') or
                         p['kd_ratio'] < 0.5]
    
    if problem_players:
        report.append("### 10.6 问题玩家详情")
        report.append("")
        report.append("| 玩家 | 职业 | 问题类型 | 击败数 | 死亡数 | KD比 | 改进建议 |")
        report.append("|------|------|----------|--------|--------|------|----------|")
        
        for player in problem_players:
            raw = player['raw_data']
            problems = []
            if raw['死亡数'] >= 10:
                problems.append("高死亡率")
            if raw['击败数'] == 0 and player['profession'] != '素问':
                problems.append("零击败")
            if player['kd_ratio'] < 0.5:
                problems.append("KD比过低")
            
            problem_type = ", ".join(problems)
            suggestion = "生存技能训练" if "高死亡率" in problems else "战斗参与度提升" if "零击败" in problems else "综合技能提升"
            
            report.append(f"| {player['name']} | {player['profession']} | {problem_type} | {raw['击败数']} | {raw['死亡数']} | {player['kd_ratio']:.2f} | {suggestion} |")
        
        report.append("")
    
    # 总结和建议
    report.append("### 10.7 防守一团分析总结")
    report.append("")
    
    # 评估防守一团表现
    if team_stats['team_kd_ratio'] >= 1.5:
        overall_rating = "优秀"
    elif team_stats['team_kd_ratio'] >= 1.0:
        overall_rating = "良好"
    elif team_stats['team_kd_ratio'] >= 0.7:
        overall_rating = "一般"
    else:
        overall_rating = "较差"
    
    report.append(f"**整体表现评级**: {overall_rating}")
    report.append(f"**核心数据**: KD比{team_stats['team_kd_ratio']:.2f}, 人伤专注度{team_stats['player_damage_focus']:.1%}")
    report.append("")
    
    report.append("**主要优势**:")
    if team_stats['player_damage_focus'] > 0.8:
        report.append("- 人伤专注度高，战术执行到位")
    if team_stats['team_kd_ratio'] > 1.0:
        report.append("- 团队KD比良好，击杀效率较高")
    
    suwen_count = len([p for p in player_performances if p['profession'] == '素问'])
    if suwen_count >= 2:
        report.append(f"- 素问配置充足({suwen_count}人)，治疗保障到位")
    
    report.append("")
    report.append("**主要问题**:")
    for issue in issues:
        report.append(f"- {issue}")
    
    report.append("")
    report.append("**改进建议**:")
    report.append("1. **提升击杀效率**: 加强团战配合，提高KD比至1.5以上")
    report.append("2. **降低死亡率**: 针对高死亡率玩家进行生存技能训练")
    report.append("3. **增强战斗参与度**: 确保所有非治疗玩家积极参与击杀")
    report.append("4. **优化素问配合**: 提升治疗效率和复活时机把握")
    report.append("5. **保持人伤专注**: 继续专注对玩家伤害，避免分散输出")
    
    return "\n".join(report)

def main():
    """主函数"""
    # 加载数据
    data = load_data('merge_XHvsQS.json')
    defense_team = load_defense_team_list('防守一团名单.txt')
    
    print("防守一团成员名单:")
    for name in defense_team:
        print(f"- {name}")
    print()
    
    # 分析防守一团表现
    analysis = analyze_defense_team_performance(data, defense_team)
    
    if "error" in analysis:
        print(f"错误: {analysis['error']}")
        return
    
    # 识别关键问题
    issues = identify_key_issues(analysis)
    
    # 生成报告
    report = generate_report(analysis, issues)
    
    # 输出报告
    print("=" * 60)
    print("防守一团表现分析报告")
    print("=" * 60)
    print(report)
    
    # 保存报告到文件
    with open('防守一团分析结果.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("\n报告已保存到 '防守一团分析结果.md'")

if __name__ == "__main__":
    main()
