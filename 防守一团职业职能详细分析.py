#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
防守一团职业职能详细分析程序
重点分析各职业在防守一团中的职能履行情况，特别是素问的治疗和复活表现
"""

import json
import statistics
from typing import Dict, List, Tuple, Any

def load_data(file_path: str) -> List[Dict]:
    """加载对局数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def load_defense_team_list(file_path: str) -> List[str]:
    """加载防守一团名单"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read().strip()
        return [name.strip() for name in content.split(',')]

def analyze_suwen_performance(suwen_players: List[Dict], team_total_deaths: int) -> Dict:
    """详细分析素问玩家表现"""
    if not suwen_players:
        return {"error": "无素问玩家"}
    
    analysis = {
        'total_healing': sum(p['治疗值'] for p in suwen_players),
        'total_revives': sum(p['化羽/清泉数'] for p in suwen_players),
        'total_deaths': sum(p['死亡数'] for p in suwen_players),
        'avg_healing_per_player': 0,
        'avg_revives_per_player': 0,
        'avg_deaths_per_player': 0,
        'revive_efficiency': 0,
        'healing_per_death_ratio': 0,
        'individual_analysis': []
    }
    
    player_count = len(suwen_players)
    analysis['avg_healing_per_player'] = analysis['total_healing'] / player_count
    analysis['avg_revives_per_player'] = analysis['total_revives'] / player_count
    analysis['avg_deaths_per_player'] = analysis['total_deaths'] / player_count
    
    # 复活效率：复活次数/团队总死亡数
    analysis['revive_efficiency'] = analysis['total_revives'] / max(team_total_deaths, 1)
    
    # 治疗死亡比
    analysis['healing_per_death_ratio'] = analysis['total_healing'] / max(analysis['total_deaths'], 1)
    
    # 个人分析
    for player in suwen_players:
        individual = {
            'name': player['玩家名字'],
            'healing': player['治疗值'],
            'revives': player['化羽/清泉数'],
            'deaths': player['死亡数'],
            'assists': player['助攻数'],
            'healing_per_death': player['治疗值'] / max(player['死亡数'], 1),
            'revive_contribution': player['化羽/清泉数'] / max(analysis['total_revives'], 1),
            'survival_rating': 'excellent' if player['死亡数'] <= 2 else 'good' if player['死亡数'] <= 5 else 'needs_improvement'
        }
        
        # 治疗效率评级
        if individual['healing_per_death'] > 100000000:  # 1亿/死亡
            individual['healing_rating'] = 'excellent'
        elif individual['healing_per_death'] > 50000000:  # 5千万/死亡
            individual['healing_rating'] = 'good'
        else:
            individual['healing_rating'] = 'needs_improvement'
        
        analysis['individual_analysis'].append(individual)
    
    return analysis

def analyze_dps_performance(dps_players: List[Dict]) -> Dict:
    """分析DPS职业表现"""
    if not dps_players:
        return {"error": "无DPS玩家"}
    
    analysis = {
        'total_kills': sum(p['击败数'] for p in dps_players),
        'total_deaths': sum(p['死亡数'] for p in dps_players),
        'total_player_damage': sum(p['对玩家伤害'] for p in dps_players),
        'total_assists': sum(p['助攻数'] for p in dps_players),
        'avg_kd_ratio': 0,
        'avg_player_damage': 0,
        'player_damage_focus': 0,
        'individual_analysis': []
    }
    
    player_count = len(dps_players)
    analysis['avg_kd_ratio'] = analysis['total_kills'] / max(analysis['total_deaths'], 1)
    analysis['avg_player_damage'] = analysis['total_player_damage'] / player_count
    
    # 人伤专注度
    total_damage = sum(p['对玩家伤害'] + p['对建筑伤害'] for p in dps_players)
    analysis['player_damage_focus'] = analysis['total_player_damage'] / max(total_damage, 1)
    
    # 个人分析
    for player in dps_players:
        total_dmg = player['对玩家伤害'] + player['对建筑伤害']
        individual = {
            'name': player['玩家名字'],
            'profession': player['职业'],
            'kills': player['击败数'],
            'deaths': player['死亡数'],
            'kd_ratio': player['击败数'] / max(player['死亡数'], 1),
            'player_damage': player['对玩家伤害'],
            'building_damage': player['对建筑伤害'],
            'assists': player['助攻数'],
            'player_damage_focus': player['对玩家伤害'] / max(total_dmg, 1),
            'damage_per_death': total_dmg / max(player['死亡数'], 1)
        }
        
        # 表现评级
        if individual['kd_ratio'] >= 5:
            individual['performance_rating'] = 'excellent'
        elif individual['kd_ratio'] >= 2:
            individual['performance_rating'] = 'good'
        elif individual['kd_ratio'] >= 1:
            individual['performance_rating'] = 'average'
        else:
            individual['performance_rating'] = 'needs_improvement'
        
        # 专注度评级
        if individual['player_damage_focus'] >= 0.9:
            individual['focus_rating'] = 'excellent'
        elif individual['player_damage_focus'] >= 0.7:
            individual['focus_rating'] = 'good'
        else:
            individual['focus_rating'] = 'needs_improvement'
        
        analysis['individual_analysis'].append(individual)
    
    # 按KD比排序
    analysis['individual_analysis'].sort(key=lambda x: x['kd_ratio'], reverse=True)
    
    return analysis

def generate_detailed_report(defense_players: List[Dict]) -> str:
    """生成详细的职业职能分析报告"""
    # 分类玩家
    suwen_players = [p for p in defense_players if p['职业'] == '素问']
    dps_players = [p for p in defense_players if p['职业'] != '素问']
    
    # 团队总死亡数
    team_total_deaths = sum(p['死亡数'] for p in defense_players)
    
    # 分析素问表现
    suwen_analysis = analyze_suwen_performance(suwen_players, team_total_deaths)
    
    # 分析DPS表现
    dps_analysis = analyze_dps_performance(dps_players)
    
    report = []
    report.append("## 防守一团职业职能详细分析")
    report.append("")
    
    # 素问职能分析
    if 'error' not in suwen_analysis:
        report.append("### 1. 素问职业职能分析")
        report.append("")
        report.append("**核心职责**: 治疗队友、复活队友(化羽)，不负责人伤输出")
        report.append("")
        report.append("#### 1.1 整体表现数据")
        report.append(f"- 素问数量: {len(suwen_players)}人")
        report.append(f"- 总治疗量: {suwen_analysis['total_healing']:,}")
        report.append(f"- 总复活次数: {suwen_analysis['total_revives']}次")
        report.append(f"- 总死亡数: {suwen_analysis['total_deaths']}次")
        report.append(f"- 平均治疗量: {suwen_analysis['avg_healing_per_player']:,.0f}/人")
        report.append(f"- 复活效率: {suwen_analysis['revive_efficiency']:.1%} (复活次数/团队总死亡数)")
        report.append(f"- 治疗死亡比: {suwen_analysis['healing_per_death_ratio']:,.0f}")
        report.append("")
        
        report.append("#### 1.2 个人表现详情")
        report.append("")
        report.append("| 玩家 | 治疗量 | 化羽数 | 死亡数 | 助攻数 | 治疗效率 | 生存评级 | 治疗评级 |")
        report.append("|------|--------|--------|--------|--------|----------|----------|----------|")
        
        for player in suwen_analysis['individual_analysis']:
            survival_rating_cn = {'excellent': '优秀', 'good': '良好', 'needs_improvement': '需改进'}[player['survival_rating']]
            healing_rating_cn = {'excellent': '优秀', 'good': '良好', 'needs_improvement': '需改进'}[player['healing_rating']]
            
            report.append(f"| {player['name']} | {player['healing']:,} | {player['revives']} | {player['deaths']} | {player['assists']} | {player['healing_per_death']:,.0f} | {survival_rating_cn} | {healing_rating_cn} |")
        
        report.append("")
        
        # 素问职能履行评估
        report.append("#### 1.3 职能履行评估")
        report.append("")
        
        if suwen_analysis['revive_efficiency'] >= 0.3:
            revive_assessment = "优秀"
        elif suwen_analysis['revive_efficiency'] >= 0.2:
            revive_assessment = "良好"
        else:
            revive_assessment = "需改进"
        
        if suwen_analysis['avg_healing_per_player'] >= 100000000:
            healing_assessment = "优秀"
        elif suwen_analysis['avg_healing_per_player'] >= 80000000:
            healing_assessment = "良好"
        else:
            healing_assessment = "需改进"
        
        report.append(f"- **治疗职能**: {healing_assessment} (平均治疗量{suwen_analysis['avg_healing_per_player']:,.0f})")
        report.append(f"- **复活职能**: {revive_assessment} (复活效率{suwen_analysis['revive_efficiency']:.1%})")
        report.append(f"- **生存能力**: {'优秀' if suwen_analysis['avg_deaths_per_player'] <= 3 else '良好' if suwen_analysis['avg_deaths_per_player'] <= 5 else '需改进'} (平均死亡{suwen_analysis['avg_deaths_per_player']:.1f}次)")
        report.append("")
    
    # DPS职业职能分析
    if 'error' not in dps_analysis:
        report.append("### 2. DPS职业职能分析")
        report.append("")
        report.append("**核心职责**: 对玩家造成伤害(人伤)，击败敌方玩家，协助团战")
        report.append("")
        report.append("#### 2.1 整体表现数据")
        report.append(f"- DPS数量: {len(dps_players)}人")
        report.append(f"- 总击败数: {dps_analysis['total_kills']}次")
        report.append(f"- 总死亡数: {dps_analysis['total_deaths']}次")
        report.append(f"- 团队KD比: {dps_analysis['avg_kd_ratio']:.2f}")
        report.append(f"- 总人伤: {dps_analysis['total_player_damage']:,}")
        report.append(f"- 人伤专注度: {dps_analysis['player_damage_focus']:.1%}")
        report.append(f"- 平均人伤: {dps_analysis['avg_player_damage']:,.0f}/人")
        report.append("")
        
        report.append("#### 2.2 个人表现详情")
        report.append("")
        report.append("| 排名 | 玩家 | 职业 | 击败数 | 死亡数 | KD比 | 人伤 | 人伤专注度 | 表现评级 |")
        report.append("|------|------|------|--------|--------|------|------|----------|----------|")
        
        for i, player in enumerate(dps_analysis['individual_analysis'], 1):
            performance_rating_cn = {
                'excellent': '优秀', 
                'good': '良好', 
                'average': '一般', 
                'needs_improvement': '需改进'
            }[player['performance_rating']]
            
            report.append(f"| {i} | {player['name']} | {player['profession']} | {player['kills']} | {player['deaths']} | {player['kd_ratio']:.2f} | {player['player_damage']:,} | {player['player_damage_focus']:.1%} | {performance_rating_cn} |")
        
        report.append("")
        
        # DPS职能履行评估
        report.append("#### 2.3 职能履行评估")
        report.append("")
        
        if dps_analysis['avg_kd_ratio'] >= 3:
            kd_assessment = "优秀"
        elif dps_analysis['avg_kd_ratio'] >= 2:
            kd_assessment = "良好"
        elif dps_analysis['avg_kd_ratio'] >= 1:
            kd_assessment = "一般"
        else:
            kd_assessment = "需改进"
        
        if dps_analysis['player_damage_focus'] >= 0.95:
            focus_assessment = "优秀"
        elif dps_analysis['player_damage_focus'] >= 0.8:
            focus_assessment = "良好"
        else:
            focus_assessment = "需改进"
        
        report.append(f"- **击杀效率**: {kd_assessment} (团队KD比{dps_analysis['avg_kd_ratio']:.2f})")
        report.append(f"- **人伤专注度**: {focus_assessment} ({dps_analysis['player_damage_focus']:.1%})")
        report.append(f"- **伤害输出**: {'优秀' if dps_analysis['avg_player_damage'] >= 80000000 else '良好' if dps_analysis['avg_player_damage'] >= 50000000 else '需改进'} (平均人伤{dps_analysis['avg_player_damage']:,.0f})")
        report.append("")
    
    # 职业配合分析
    report.append("### 3. 职业配合分析")
    report.append("")
    
    if 'error' not in suwen_analysis and 'error' not in dps_analysis:
        # 计算配合效率
        dps_survival_rate = 1 - (dps_analysis['total_deaths'] / max(len(dps_players) * 10, 1))  # 假设10次死亡为基准
        healing_coverage = suwen_analysis['total_healing'] / max(dps_analysis['total_player_damage'] * 0.1, 1)  # 治疗量应为输出的10%左右
        
        report.append("#### 3.1 素问与DPS配合效果")
        report.append(f"- **治疗覆盖率**: {min(healing_coverage, 2):.1%}")
        report.append(f"- **DPS生存率**: {max(dps_survival_rate, 0):.1%}")
        report.append(f"- **复活支援**: 平均每{team_total_deaths/max(suwen_analysis['total_revives'], 1):.1f}次死亡获得1次复活")
        report.append("")
        
        # 配合评级
        if healing_coverage >= 0.8 and dps_survival_rate >= 0.7:
            cooperation_rating = "优秀"
        elif healing_coverage >= 0.6 and dps_survival_rate >= 0.5:
            cooperation_rating = "良好"
        else:
            cooperation_rating = "需改进"
        
        report.append(f"**整体配合评级**: {cooperation_rating}")
        report.append("")
    
    # 改进建议
    report.append("### 4. 职业职能改进建议")
    report.append("")
    
    if 'error' not in suwen_analysis:
        report.append("#### 4.1 素问改进建议")
        for player in suwen_analysis['individual_analysis']:
            if player['healing_rating'] == 'needs_improvement' or player['survival_rating'] == 'needs_improvement':
                suggestions = []
                if player['healing_rating'] == 'needs_improvement':
                    suggestions.append("提高治疗量输出")
                if player['survival_rating'] == 'needs_improvement':
                    suggestions.append("加强生存技能")
                if player['revives'] < 5:
                    suggestions.append("提高复活意识")
                
                if suggestions:
                    report.append(f"- **{player['name']}**: {', '.join(suggestions)}")
        report.append("")
    
    if 'error' not in dps_analysis:
        report.append("#### 4.2 DPS改进建议")
        for player in dps_analysis['individual_analysis']:
            if player['performance_rating'] in ['needs_improvement', 'average']:
                suggestions = []
                if player['kd_ratio'] < 2:
                    suggestions.append("提高击杀效率")
                if player['player_damage_focus'] < 0.8:
                    suggestions.append("专注人伤输出")
                if player['deaths'] > 8:
                    suggestions.append("加强生存技能")
                
                if suggestions:
                    report.append(f"- **{player['name']}**: {', '.join(suggestions)}")
        report.append("")
    
    return "\n".join(report)

def main():
    """主函数"""
    # 加载数据
    data = load_data('merge_XHvsQS.json')
    defense_team = load_defense_team_list('防守一团名单.txt')
    
    # 筛选防守一团成员
    defense_players = [p for p in data if p['玩家名字'] in defense_team and p['队伍'] == '星河若梦']
    
    if not defense_players:
        print("错误: 未找到防守一团成员数据")
        return
    
    # 生成详细报告
    report = generate_detailed_report(defense_players)
    
    # 输出报告
    print(report)
    
    # 保存报告到文件
    with open('防守一团职业职能详细分析.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("\n详细分析报告已保存到 '防守一团职业职能详细分析.md'")

if __name__ == "__main__":
    main()
